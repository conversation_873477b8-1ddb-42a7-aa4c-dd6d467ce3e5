<template>
  <div class="body_wrap">
    <a-spin :spinning="loading">
      <div class="p_wrap">
        <div class="title_wrap" v-if="!，">
          <a-breadcrumb>
            <a-breadcrumb-item><a href="void:0" @click="router.push({name:'projectList'})"> < 返回</a></a-breadcrumb-item>
            <a-breadcrumb-item>测算结果</a-breadcrumb-item>
          </a-breadcrumb>
          <div class="btn_wrap">
            <a-button class="btn_item" size2="small" @click="downloadReport" :loading="loadingPdf">下载报告</a-button>
            <a-button class="btn_item" size2="small" @click="exportWordDoc" :loading="loadingExport">数据导出Word</a-button>
            <a-button class="btn_item" size2="small" @click="exportChartData" :loading="loadingDataExport">运行数据导出Excel</a-button>
            <a-button class="btn_item" size2="small" @click="editAndSaveSolution">{{isEditing ? '重新测算' : '修改方案'}}</a-button>
            <a-button
              class="btn_item" size2="small"  type="primary" @click="visiableSaveModal=true"
              v-if="type !== 'list'&&visiableSaveBtn">
              保存方案
            </a-button>
            <!-- <a-button class="btn_item" size2="small" type="primary" @click="analysis" disabled>经济性分析</a-button> -->
          </div>
      
        </div>
        
          <div class="content_wrap" id="content_wrap">
            <div id="tmp_title" class="tmp_title" v-show="false">
              <!-- <img width="80px" src="data:image/png;base64,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"> -->
              <div class="t_word" >{{baseConfig.title}}报告</div>
            </div>
            <div class="part_wrap">
              <div class="p_title">绿电绿氢系统参数</div>
              <div class="params_card_list">
                <template v-for="item in paramsList(solutionParams.topology)" :key="item.title">
                  <div class="params_card_item" v-if="item.visible">
                    <div class="params_card_content">
                      <div class="params_card_title">{{ item.title }}</div>
                      <div class="params_list">
                        <div class="params_item" v-for="i in item.params">
                          <div class="params_key">{{ i.label }}{{ i.unit ? `(${i.unit})` : '' }}:</div>
                          <div class="params_value">
                            {{ solutionParams[i.name] !== undefined && solutionParams[i.name] !== null ?
                              (i.unit === '%' ? (solutionParams[i.name] * 100).toFixed(2) : solutionParams[i.name]) :
                              '—' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <div class="part_wrap">
              <div class="p_title">绿电绿氢系统结果</div>
              <div class="card_list">
                <div class="card_item" v-for="item in (detailData?.hs_plan?.length ? outputInfo() : (outputInfo().filter(i => i.key!== 'alkStorage')))">
                  <div class="card_title_wrap">
                    <div class="c_pic">
                      <img :src="item.pic" />
                    </div>
                    <div class="c_title">
                      <div class="main_title">{{ item.mainTitle }}</div>
                      <div
                        class="vice_title" v-if="item.viceTitle.name">
                        {{ `${item.viceTitle.label}(${item.viceTitle.unit}): ${detailData[item.viceTitle.name]}` }}
                      </div>
                    </div>
                  </div>
                  <div v-if="item.key==='alk'">
                    <a-table
                      :columns="alkColumns()" :data-source="alkTableData" :pagination="false"
                      size="small"
                    >
                    </a-table>
                  </div>
                  <div v-else-if="item.key==='alkStorage'">
                    <div class="key_val_list">
                      <div class="key_val_item">
                        <div class="key">储罐厂商:</div>
                        <div class="val">{{ deviceList?.alkStorage?.[0]?.manufacturer}}</div>
                      </div>
                      <div class="key_val_item">
                        <div class="key">储罐型号:</div>
                        <div class="val">{{ deviceList?.alkStorage?.[0]?.model}}</div>
                      </div>
                      <div class="key_val_item">
                        <div class="key">储罐容量(m³):</div>
                        <div class="val">{{ deviceList?.alkStorage?.[0]?.volume}}</div>
                      </div>
                      <div class="key_val_item">
                        <div class="key">储罐数量:</div>
                        <div class="val" v-if="isEditing">
                          <a-input-number v-model:value="newEditValue['hs_capacity']" size="small" />
                        </div>
                        <div class="val" v-else>{{ detailData?.hs_plan?.[0]?.number}}</div>
                      </div>
                    </div>
                  </div>
                  <div class="key_val_list" v-else>
                    <div class="key_val_item" v-for="i in item.list">
                      <div class="key">{{i.label}}{{i.unit ? `(${i.unit})` : '' }}:</div>
                      <div class="val" v-if="i.editable&&isEditing&&(detailData[i.name]!==undefined&&detailData[i.name]!==null)">
                        <a-input-number
                          style2="width:100%"
                          size="small"
                          v-if="i.numberType === 'ratio'" 
                          v-model:value="newEditValue[i.name]"
                          :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                          :parser="value => Decimal(value).div(Decimal(100)) "
                          :min="0"
                          :max="1"
                          :step="0.01"
                        />
                        <a-input-number
                          v-if="i.numberType !== 'ratio'" 
                          v-model:value="newEditValue[i.name]" size="small"
                        />
                      </div>
                      <div v-else class="val">
                        {{ (detailData[i.name] === undefined || detailData[i.name] === null) ? '—'  : (i.numberType==='ratio' ? Decimal(detailData[i.name]).mul(Decimal(100)) : detailData[i.name]) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="part_wrap">
              <div class="p_title">绿电绿氢系统配置-功率分配曲线</div>
              <div class="chart_list">
                <div class="chart_card_combine">
                  <!-- <div class="title_wrap_combine">功率运行曲线</div> -->
                  <line-chart :data="combineData" class="chart_wrap_combine" />
                </div>
                
              </div>
              <div class="chart_list">
                <div class="chart_card" v-for="item in mainChartList">
                  <div class="title_wrap">
                    <div class="tag" v-if="item.tag">{{ item.tag }}</div>
                    <div>{{ item.title }}</div>
                  </div>
                  <div v-if="item?.chartData?.series?.[0].data?.length">
                    <line-chart :data="item.chartData" class="chart_wrap" />
                  </div>
                  <div v-else>
                    <a-empty />
                  </div>
                </div>
              </div>
            </div>
            <div class="part_wrap">
              <div class="p_title">绿电绿氢系统配置-制氢运行曲线</div>
              <div class="chart_list">
                <div class="chart_card" v-for="item in viceChartList">
                  <div class="title_wrap">
                    <div class="tag" v-if="item.tag">{{ item.tag }}</div>
                    <div>{{ item.title }}</div>
                  </div>
                  <div v-if="item?.chartData?.series?.[0].data?.length">
                    <line-chart :data="item.chartData" class="chart_wrap" />
                  </div>
                  <div v-else>
                    <a-empty />
                  </div>
                </div>
              </div>
            </div>
            <div id="tmp_tail" class="tmp_tail" v-show="false">
              <div>
                <div class="t_name">{{ baseConfig.company }}</div>
                <div class="t_date">{{ curDate }}</div>  
              </div>
            </div>
          </div>
      </div>
    </a-spin>
    <a-modal v-model:open="visiableSaveModal" title="保存当前项目方案" @ok="submitSave" :confirm-loading="confirmSaveLoading">
      <div>
        <a-form
          labelAlign="left2"
          ref="formRef"
          :model="formState"
          name="basic"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
          @finish="onFinish"
          @finishFailed="onFinishFailed"
        >
          <a-form-item
            v-if="solutions?.length >= 5"
            label="被替换方案"
            name="selReplaceSolution"
            :rules="[{ required: true, message: 'Please select!' }]"
            tooltip="每个项目最多5个方案，可用新方案替换其中之一"
          >
            <a-select
              v-model:value="formState.selReplaceSolution"
              style2="width: 120px"
              @change="handleChange"
            >
              <a-select-option
                v-for="i in solutions"
                :value="i.value">{{ i.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="方案名称"
            name="name"
            :rules="[{ required: true, message: 'Please input!' }]"
          >
            <a-input v-model:value="formState.name" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 数据导出组件 -->
    <ExportData
      ref="exportDataRef"
      :detailData="detailData"
      :solutionParams="solutionParams"
      :chartData="chartDataForExport"
    />

    <!-- Word导出组件 -->
    <ExportWord
      ref="exportWordRef"
      :detailData="detailData"
      :solutionParams="solutionParams"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, onUnmounted  } from 'vue'
import { message } from 'ant-design-vue'
import Decimal from "decimal.js"
import dayjs from 'dayjs'
import {
  getProjects, getResult, saveSolution, mdfCalcParams, getSolutionList
  , getDevice, getSolution
} from '@/api/project'
import { useRouter, useRoute  } from 'vue-router'
import {  outputInfo, downloadPdf, alkColumns, paramsList } from './util'
import LineChart from '@/components/LineChart/index.vue'
import ExportData from './exportExcel.vue'
import ExportWord from './exportWord.vue'
import { CodeSandboxCircleFilled } from '@ant-design/icons-vue'
import { baseConfig } from '@/config/baseConfig'

const RUNNING = 'RUNNING'
const SUCCESS = 'SUCCESS'
const FAIL = 'FAIL'
const router = useRouter()
const route = useRoute()
const loading = ref(false)
const loadingExport = ref(false)
const detailData = ref({})
const basicInfoList = ref([])
const mainChartList = ref([])
const viceChartList = ref([])
const visiableSaveModal = ref(false)
const formRef = ref()
const isEditing = ref(false)
const alkTableData = ref([])
const visiableSaveBtn = ref(true)
const curDate = ref()
const solutionParams = ref({})

const loadingPdf = ref(false)
const loadingDataExport = ref(false)
const exportDataRef = ref()
const exportWordRef = ref()
const solutions = ref([
])
const confirmSaveLoading = ref(false)
const deviceList = ref({
  alk: [],
  bat: [],
  alkStorage: []
})
const storeItem = ref({})

const projectId = computed(() => route.query.projectId ? parseInt(route.query.projectId) : undefined)
const solutionId = computed(() => route.query.solutionId ? parseInt(route.query.solutionId) : undefined)
const taskId = computed(() => route.query.taskId ? route.query.taskId : undefined)
const type = computed(() => route.query.type ? route.query.type : undefined)
const timer = ref()
const status = ref('RUNNING') // 0- success, 12-running, 14-fail
const newEditValue = ref({
  pv_capacity: undefined,
  wind_capacity: undefined,
  es_capacity: undefined,
  ele_capacity: undefined,
  grid_down_quantity: undefined,
  taskId: taskId.value,
  solutionId: solutionId.value,
  hs_capacity: undefined
})
const combineData = ref({})
const rawChartData = ref({})

const isPreviewMode = computed(() => {
  return route.query.viewType === '1'
})

// 为导出准备的图表数据
const chartDataForExport = computed(() => {
  return {
    ...rawChartData.value,
    ...combineData.value
  }
})

const createChartConfig = (params, newOption = {}) => {
  const zoomRange = ref([40, 50])
  const option = {
    title: params?.title,
    tag: params?.tag,
    chartData: {
      grid: {
        top: '5%',    // 调整顶部留白
        bottom: '10%', // 调整底部留白
        left: '3%',
        right: '5%',
        containLabel: true
      },
      series: [
        {
          data: params?.yData,
          type: 'line',
          lineStyle: {
            normal: {
              color: baseConfig.baseColor, //  #15a675 #005236 #1677ff
            }
          },
        }
      ],
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: zoomRange.value[0],
          end: zoomRange.value[1],
          // xAxisIndex: [0, 1]
          bottom: '0px'
        },
        {
          type: 'inside',
        }
      ],
      ...newOption
    }
  }
  return option
}

const createIntegratedChartOption = (params) => {
  const zoomRange = ref([40, 50])
  const { xData,pvData,windData,combinedData,storageData,gridData,electrolyzerPower,socData, ele_all_output } = params
  const option = {
    title: {
        text: '',
        left: 'left',
        top: '0px',
        textStyle: {
            fontSize: 16,
            fontWeight: 500,
            color: '#2c3e50'
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '12%',
        top: '70px',  // 相应调整图表顶部边距
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985'
            }
        },
        // formatter: function(params) {
        //     // 找到电解槽和储能的数据
        //     const electrolyzerData = params.filter(p => p.seriesName.includes('电解槽'));
        //     const storageData = params.find(p => p.seriesName === '储能');
        //     const gridData = params.find(p => p.seriesName === '电网');
        //     const socData = params.find(p => p.seriesName === '储能SOC');
            
        //     // 计算电解槽总功率
        //     const totalElectrolyzerPower = electrolyzerData.reduce((sum, item) => sum + item.value, 0);
            
        //     // 格式化时间
        //     const totalMinutes = parseInt(params[0].axisValue || 0);
        //     const days = Math.floor(totalMinutes / (24 * 60));
        //     const hour = Math.floor((totalMinutes % (24 * 60)) / 60);
        //     const minute = totalMinutes % 60;
        //     const time = `Day ${days+1} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            
        //     // 构建提示信息
        //     let result = `时间: ${time}<br/>`;
            
        //     // 添加电解槽功率及占比
        //     electrolyzerData.forEach(item => {
        //         const percentage = (item.value / totalElectrolyzerPower * 100).toFixed(1);
        //         result += `${item.seriesName}: ${(item.value/1000).toFixed(2)} MW (${percentage}%)<br/>`;
        //     });
            
        //     // 添加总电解功率
        //     result += `<br/>总电解功率: ${(totalElectrolyzerPower/1000).toFixed(2)} MW<br/>`;
            
        //     // 添加储能功率和 SOC
        //     if (storageData) {
        //         const storagePower = storageData.value;
        //         const storageMode = storagePower > 0 ? '放电' : '充电';
        //         result += `储能${storageMode}: ${Math.abs(storagePower/1000).toFixed(2)} MW`;
        //         if (socData) {
        //             result += ` (SOC: ${socData.value.toFixed(1)}%)<br/>`;
        //         } else {
        //             result += '<br/>';
        //         }
        //     }
            
        //     // 添加电网功率
        //     if (gridData) {
        //         const gridPower = gridData.value;
        //         const gridMode = gridPower > 0 ? '下网' : '上网';
        //         result += `电网${gridMode}: ${Math.abs(gridPower/1000).toFixed(2)} MW<br/>`;
        //     }
            
        //     return result;
        // }
    },
    legend: {
        data: ['光伏', '风机', '风光一体', '储能', '电网', '储能SOC', '电解槽总'],
        top: 20,
        type: 'scroll',  // 添加滚动功能
        pageButtonPosition: 'end',
        padding: [5, 0],
        selected: {
          '光伏': false,
          '风机': false,
          '风光一体': true,
          '储能': true,
          '电网': true,
          '电解槽': true,
          '储能SOC': true
        }
    }, 
    xAxis: {
        type: 'category',
        name: '时间',
        interval: 1,
        // data: xData,
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed'
            }
        },
        nameTextStyle: {
          padding: [0, 0, 0, 20]
        },
        axisLabel: {
            // formatter: value => {
            //     const totalMinutes = parseInt(value);
            //     const days = Math.floor(totalMinutes / (24 * 60));
            //     const hour = Math.floor((totalMinutes % (24 * 60)) / 60);
            //     return `D${days+1} ${hour.toString().padStart(2, '0')}:00`;
            // }
        }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 40,
        end: 45,  // 默认显示前两天（约30%的数据）
        bottom: 20,
        height: 20,
        borderColor: 'transparent',
        backgroundColor: '#f8fafc',
        fillerColor: 'rgba(43, 133, 228, 0.1)',
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#2b85e4',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowOffsetX: 1,
          shadowOffsetY: 1
        }
      },
      {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: 30,
          // zoomOnMouseWheel: 'shift'
      }
    ],

    yAxis: [
        {
            type: 'value',
            name: '功率(MW)',
            nameTextStyle: {
                padding: [0, 0, 0, 40]  // 调整单位位置
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        {
            type: 'value',
            name: 'SOC(%)',
            nameTextStyle: {
                padding: [0, 40, 0, 0]  // 调整单位位置
            },
            splitLine: {
                show: false
            }
        }
    ],
    series: [
        {
            name: '光伏',
            type: 'line',
            smooth: true,
            symbol: 'none',
            data: pvData,
            // lineStyle: {
            //   type: 'solid',
            //   // color: 'blue'
            // },
            itemStyle: {
              normal: {
                color: 'blue',
                lineStyle: {
                  color: 'blue', //  #15a675 #005236 #1677ff
                }
              }
            },
        },
        {
          name: '风机',
          type: 'line',
          smooth: true,
          symbol: 'none',
          data: windData,
          itemStyle: {
            normal: {
              color: 'pink',
              lineStyle: {
                color: 'pink', //  #15a675 #005236 #1677ff
              }
            }
          },
        },
        {
          name: '风光一体',
          type: 'line',
          smooth: true,
          symbol: 'none',
          // lineStyle: {
          //   width: 2,
          //   type: 'solid'
          // },
          // itemStyle: {
          //   // color: '#91CC75'
          // },
          itemStyle: {
            normal: {
              color: '#39cfed',
              lineStyle: {
                width: 2,
                color: '#39cfed', //  #15a675 #005236 #1677ff
              }
            }
          },
          data: combinedData
        },
        {
            name: '储能',
            type: 'line',
            smooth: true,
            symbol: 'none',
            data: storageData,
            itemStyle: {
              normal: {
                color: '#B8860B',
                lineStyle: {
                  width:2,
                  color: '#B8860B', //  #15a675 #005236 #1677ff
                }
              }
            },
        },
        {
            name: '电网',
            type: 'line',
            smooth: true,
            symbol: 'none',
            data: gridData, // #FA8072
            itemStyle: {
              normal: {
                color: '#FA8072',
                lineStyle: {
                  color: '#FA8072', //  #15a675 #005236 #1677ff
                }
              }
            },
        },
        {
            name: '电解槽总',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
                focus: 'series'
            },
            data:  electrolyzerPower,
            itemStyle: {
              normal: {
                color: baseConfig.baseColor,
                lineStyle: {
                  color: baseConfig.baseColor, //  #15a675 #005236 #1677ff
                }
              }
            },
        },
        {
            name: '储能SOC',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'none',
            // lineStyle: {
            //     width: 2,
            //     type: 'dashed'
            // },
            // itemStyle: {
            //     // color: '#005236'
            // },
            data: socData,
            itemStyle: {
              normal: {
                color: '#FFDEAD',
                lineStyle: {
                  type: 'dashed',
                  color: '#FFDEAD', //  #15a675 #005236 #1677ff
                }
              }
            },
        }
    ]
  };
  if (ele_all_output?.length) {
    ele_all_output.forEach((ele, n) => {
      option.legend.data.push(`电解槽${n+1}`)
      option.legend.selected[`电解槽${n+1}`] = false
      option.series.push( {
        name: `电解槽${n+1}`,
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: ele.data
      })
    })
  }
  return option
}
const formState = reactive({
  selReplaceSolution: undefined
});
const submitSave = async () => {
  const values = await formRef.value.validateFields()
  confirmSaveLoading.value = true
  const { code, msg } = await saveSolution({
    taskId: taskId.value,
    projectId: projectId.value,
    solutionId: solutions.value.length >= 5 ? formState.selReplaceSolution : undefined,
    solutionName: values.name
  })
  confirmSaveLoading.value = false
  if (code === 0) {
    visiableSaveModal.value = false
    message.success('保存成功')
    status.value = SUCCESS
    visiableSaveBtn.value = false
    router.push({
      name: 'projectDetail',
      query: {
        ...route.query,
        type: 'list'
      }
    })
  } else {
    visiableSaveModal.value = false
    message.error(msg)
  }
  // console.log('form:', values, formState)
}

const downloadReport = async () => {
  loadingPdf.value = true
  setTimeout(async () => {
    await downloadPdf('content_wrap')
    loadingPdf.value = false
  }, 300)
}

// 导出图表数据
const exportChartData = async () => {
  if (!exportDataRef.value) {
    message.error('导出组件未初始化')
    return
  }

  loadingDataExport.value = true
  try {
    await exportDataRef.value.exportPowerAllocationData()
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  } finally {
    loadingDataExport.value = false
  }
}
const initEditData = () => {
  ['pv_capacity', 'wind_capacity', 'es_capacity', 'ele_capacity', 'grid_down_radio', 'grid_up_radio']
    .forEach(item => {
      newEditValue.value[item] = detailData.value[item]
    })
  newEditValue.value['hs_capacity'] = detailData.value?.hs_plan?.[0]?.number

}


const editAndSaveSolution = async () => {
  if (!isEditing.value) {
    initEditData()
    // console.log('e:', newEditValue.value)
    isEditing.value = true
  } else {
    const { code, msg, data } = await mdfCalcParams(newEditValue.value)

    if (code === 0) {
      message.success('已提交重新运行')
      const link = router.resolve({
        name: 'projectDetail',
        query: {
          taskId: data.taskId,
          projectId:  projectId.value,
          solutionId: solutionId.value
        }
      })
      window.open(link.href, '_blank')
    } else {
      message.error(msg)
    }
    isEditing.value = false
  }
}

// 导出Word文档
const exportWordDoc = async () => {
  if (!exportWordRef.value) {
    message.error('Word导出组件未初始化')
    return
  }

  loadingExport.value = true
  try {
    await exportWordRef.value.exportWordDoc()
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  } finally {
    loadingExport.value = false
  }
}



const getSolutions = async () => {
  // console.log('projectId:', projectId.value)
  if (projectId.value) {
    const { code, msg, data: { total, result }} = await getSolutionList({ projectId: projectId.value })
    if (code === 0) {
      solutions.value = result.map(i => {
        return {
          label: i.name,
          value: i.id
        }
      })
    } else {
      console.error(msg)
    }
  }
}
const getAlkList = async () => {
  const { code, msg, data } = await getDevice({ type: 4 })
  deviceList.value.alk = data.result.map(i => {
    const { baseInfo, params } = i
    return {
      ...baseInfo,
      ...params,
    }
  })
}
const getStoreList = async () => {
  const { code, msg, data } = await getDevice({ type: 5 })
  deviceList.value.alkStorage = data.result.map(i => {
    const { baseInfo, params } = i
    return {
      ...baseInfo,
      ...params,
    }
  })
  // console.log('a:', deviceList.value.alkStorage)
}

const initData = async () => {

  loading.value = true
  await getAlkList()
  await getStoreList()
  const { code, data, msg } = await getResult({
    projectId: projectId.value,
    solutionId: solutionId.value,
    taskId: taskId.value
  })
  if (code === 0) {
    loading.value = false
    
    detailData.value = data
    // data['ele_plan'] = [
    //   { devId: 5, number: 2 },
    //   { devId: 7, number: 10 },
    // ]
    // console.log('alk data:', data.ele_plan)
    // console.log('alk:',deviceList.value.alk )

    alkTableData.value = data['ele_plan']?.map(item => {
      const alkItem = deviceList.value.alk?.find(i => i.id === item.devId)

      return {
        name: `${alkItem.manufacturer}_${alkItem.model}`,
        number: item.number,
        ...alkItem
      }
    })
    const { project = {}, solution = {}, calcParams = {} } = data?.input_param_overview
    solutionParams.value = {
      ...project,
      ...calcParams,
      ...solution,
      projectName: project.name,
      projectDesc: project.desc
    }

    status.value = SUCCESS
    if (type.value === 'list') {
      message.success('加载数据成功')
    } else {
      message.success('运行成功。请及时保存方案数据，防止数据丢失！')
      // console.log('status:', status.value)
      visiableSaveModal.value = true
    }

      // 储罐-厂商
    detailData.value.alkStoreCompany = deviceList.value?.alkStorage?.[0]?.manufacturer 
    // 储罐-容量
    detailData.value.volume = deviceList.value?.alkStorage?.[0]?.volume
        // 储罐-数量
    detailData.value.alkStoreNum = detailData.value?.hs_plan?.[0]?.number
    // 电解槽
    detailData.value.alkList = alkTableData.value
    console.log(' detailData.value.alkStoreCompany:',   deviceList.value?.alkStorage?.[0]?.manufacturer )
    // message.success('运行成功')
  } else if (code === 12) {
    status.value = RUNNING
    return
  } else {
    loading.value = false
    status.value = FAIL
    message.error(msg)
    return
  }
  
  const {
    pv_output, wind_output, es_soc, abort_ge, ele_output, h2_product,
    run_hours, abort_h2, store_h2, supply_h2, abort_power, es_output, ge_output, grid_output, produce_h2, ele_all_output
  } = data

  // 保存原始数据用于导出
  rawChartData.value = {
    pvData: pv_output?.data,
    windData: wind_output?.data,
    combinedData: ge_output?.data,
    storageData: es_output?.data,
    gridData: grid_output?.data,
    electrolyzerPower: ele_output?.data,
    socData: es_soc?.data,
    ele_all_output: ele_all_output,
    produce_h2: produce_h2?.data,
    supply_h2: supply_h2?.data,
    store_h2: store_h2?.data,
    abort_ge: abort_ge?.data,
    run_hours: run_hours?.data
  }

  mainChartList.value = [
    // createChartConfig({ title: '功率曲线', tag: '光伏', yData: pv_output?.data }),
    // createChartConfig({ title: '功率曲线', tag: '风机', yData: wind_output?.data }),
    // createChartConfig({ title: '功率曲线', tag: '储能', yData: es_output?.data }),
    // createChartConfig({ title: '电解槽出力曲线', tag: '电解槽', yData: ele_output?.data }),
  ]
  viceChartList.value = [
    // createChartConfig({ title: '储能SOC曲线', tag: '储能', yData: es_soc?.data }),
    // createChartConfig({ title: '日产氢量曲线', tag: '电解槽', yData: h2_product?.data  }), // 365天
    createChartConfig({
        title: '制氢曲线(Nm³)',
        // name: '功率(MW)',
        tag: '制氢',
        yData: []
      },
      {
          // colors: ['rgba(44, 170, 137, 1)', '#1677ff'],
          legend: {
            data: ['产氢量', '供氢量'],
            // top: 20,
            // type: 'scroll',  // 添加滚动功能
            // pageButtonPosition: 'end',
            padding: [5, 0],
            // colors: ['rgba(44, 170, 137, 1)', '#1677ff']
          }, 
          series: [
            {
              name: '产氢量',
              data: produce_h2?.data,
              type: 'line',
              itemStyle: {
                normal: {
                  color: 'rgba(44, 170, 137, 1)',
                  lineStyle: {
                    color: 'rgba(44, 170, 137, 1)', //  #15a675 #005236 #1677ff
                  }
                }
              },
            },
            detailData.value?.hs_plan?.length ? {
              name: '供氢量',
              data: supply_h2?.data,
              type: 'line',
              itemStyle: {
                normal: {
                  color: '#1677ff', //  #15a675 #005236 #1677ff
                  lineStyle: {
                    color: '#1677ff', //  #15a675 #005236 #1677ff
                  }
                }
              },
            } : null
          ],
          dataZoom: [
            {
              show: true,
              realtime: true,
              start: 40,
              end: 45,
              // xAxisIndex: [0, 1]
              bottom: '0px'
            },
            {
              type: 'inside',
            }
          ],
        }
      ), // 8760
    detailData.value?.hs_plan?.length ? createChartConfig({ title: '储氢曲线(%)', tag: '储氢', yData: store_h2?.data?.map(i=>(i*100).toFixed(2)) }) : null,
    createChartConfig({ title: '弃电量曲线(MWh)', tag: '弃电', yData: abort_ge?.data }),
    createChartConfig({ title: '日运行小时数(h)', tag: '运行小时', yData: run_hours?.data }),
    // createChartConfig({ title: '弃氢曲线', tag: '', yData: abort_h2.data }),
    // createChartConfig({ title: '供氢曲线', tag: '储氢', yData: supply_h2?.data }),
    // createChartConfig({ title: '弃氢对应的弃电曲线', tag: '', yData: abort_power.data }),
  ]
  viceChartList.value = viceChartList.value.filter(i => !!i)
  combineData.value = createIntegratedChartOption({
    // xData,
    ele_all_output,
    pvData: pv_output?.data,
    windData: wind_output?.data,
    combinedData: ge_output?.data,
    storageData: es_output?.data,
    gridData: grid_output?.data,
    electrolyzerPower: ele_output?.data,
    socData: es_soc?.data?.map(i=> (i*100).toFixed(2))
  })
  const pv_capex = detailData.value.pv_capex || 0
  const wind_capex = detailData.value.wind_capex || 0
  const es_capex = detailData.value.es_capex || 0
  const h2_capex = detailData.value.h2_capex || 0
  const hs_capex = detailData.value.hs_capex || 0

  console.log('光伏Capex(万元)：', pv_capex)
  console.log('风机Capex(万元)：', wind_capex)
  console.log('储能Capex(万元)：', es_capex)
  console.log('制氢Capex(万元)：', h2_capex)
  console.log('储罐Capex(万元)：', hs_capex)
  console.log('总Capex(万元):', pv_capex + wind_capex + es_capex + h2_capex + hs_capex)
  // console.log('detailData:', detailData.value, combineData.value)
}

const getBaseData = async () => {
  await initData()
  if (status.value !== RUNNING) {
    clearInterval(timer.value)
    return
  } else if (status.value === RUNNING) {
    timer.value = setInterval(async() => {
      await initData()
      if (status.value !== RUNNING) {
        clearInterval(timer.value)
      }
    }, 3 * 1000)
  }
}

onMounted(() => {
  // initData()
  getBaseData()
  getSolutions()

  curDate.value = dayjs().format('YYYY-MM-DD')
})

onUnmounted(() => {
  window.addEventListener('beforeunload', function (event) {
    return null
  })
  clearInterval(timer.value)
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px;
  position: relative;
  // * {
  //   font-size: 12px;
  // }
}
.p_wrap {
  .title_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn_wrap {
      .btn_item {
        margin-left: 10px;
      }
    }
  }
  
}
.part_wrap {
  margin-bottom: 20px;
}
.content_wrap {
  margin-top: 20px;
  .tmp_title {
    margin: 15px;
    display: flex;
    justify-content:center;
    align-items: center;
    font-size: 24px;
    font-weight:bold;
    .t_word {
      margin-left: 15px;
    }
  }
  .tmp_tail {
    margin: 15px 20px 15px 0;
    display:flex;
    justify-content:end;
    font-size: 16px;
    font-weight:bold;
    flex-wrap: wrap;
    .t_name {
      margin-bottom: 10px;
    }
    .t_date {
    }
  }
  .p_title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  // 绿电绿氢系统参数样式
  .params_card_list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px; // 负边距抵消卡片的margin
  }

  .params_card_item {
    width: 25%; // 固定每行4个
    padding: 0 8px; // 左右内边距
    margin-bottom: 16px;
    box-sizing: border-box;

    .params_card_content {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #f0f0f0;
      height: 100%; // 确保同行卡片高度一致

      .params_card_title {
        font-size: 14px;
        font-weight: 600;
        color: @baseColor;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .params_list {
        .params_item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .params_key {
            color: #666;
            flex: 1;
            margin-right: 8px;
            word-break: break-word;
          }

          .params_value {
            color: #333;
            font-weight: 500;
            text-align: right;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .card_list {
    display: flex;
    flex-wrap: wrap;
  }
  .card_item {
    width: 32%;
    background-color: #fff;
    margin: 0 1% 1% 0;
    &:nth-child(3n) {
      margin-right: 0
    }
    padding: 20px;
    .card_title_wrap {
      display: flex;
      align-items: center;
    }
    .c_pic {
      width: 15%;
      img {
        width: 100%;
      }
    }
    .c_title {
      margin-left: 10px;
      color: @baseColor;
      font-size: 18px;
      .vice_title {
        margin-top: 5px;
        font-size: 12px;
      }
    }
    .key_val_list {
      margin-top: 15px;
      display: flex;
      flex-wrap: wrap;
      // justify-content: center;
      .key_val_item {
        width: 90%;
        // max-width: 400px;
        min-width: 200px;
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-top: 10px;
        // margin-right: 5px;
        .key {
          width: 50%;
        }
        .val { 
          margin-left: 10px;
        }
      }
    }
    @media (min-width: 1300px) {
      .key_val_list {
        margin-top: 15px;
        display: flex;
        flex-wrap: wrap;
        // justify-content: center;
        .key_val_item {
          width: 50%;
          // max-width: 400px;
          min-width: 200px;
          display: flex;
          align-items: center;
          font-size: 12px;
          margin-top: 10px;
          // margin-right: 5px;
          .key {
            width: 50%;
          }
          .val { 
            margin-left: 10px;
          }
        }
      }
    }
  }
  .chart_list {
    display: flex;
    flex-wrap: wrap;
    .chart_card {
      width: 49%;
      margin: 0 2% 2% 0;
      padding: 15px 15px;
      background-color: #fff;
      &:nth-child(2n) {
        margin-right: 0;
        // background-color: red;
      }
      .title_wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 0 0 10px 0;
      }
      .tag {
        margin:  0 10px 0 0;
        padding: 2px 5px;
        border-radius: 3px;
        background: @baseColor;
        color: #fff;
      }
    }
    .chart_wrap {
      // width: 100%;
      height: 200px;
      
    }
    .chart_card_combine {
      width: 100%;
      height: 500px;
      padding: 5px 20px;
      background-color: #fff;
      margin-bottom: 20px;
      .title_wrap_combine {
        font-size: 20px;
        margin: 10px 0 0 10px;
      }
    }
    .chart_wrap_combine {
      background-color: #fff;
    }
  }
}

</style>
